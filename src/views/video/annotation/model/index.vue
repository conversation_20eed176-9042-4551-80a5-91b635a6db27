<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>
    </template>

    <!-- 类型（并联/串联）显示 -->
    <template #table:value3:simple="{ row }">
      <el-tag :type="getCombinationTagType(row.value3)">
        {{ row.value3 }}
      </el-tag>
    </template>

    <!-- 状态显示 -->
    <template #table:value7:simple="{ row }">
      <el-switch v-model="row.value7" active-value="启用" inactive-value="禁用" @change="(value) => onStatusChange(value, row)"></el-switch>
    </template>

    <!-- 算子组合显示 -->
    <template #table:value8:simple="{ row }">
      <div class="model-info">
        {{ row.value8 }}
      </div>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleCopy(row)">
        复制
      </el-button>
    </template>

    <!-- 算子类型下拉列表 -->
    <template #after>
    </template>

    <template #info:before></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { operatorCombinationType, operatorChainStatus, operatorTypes, businessType } from '@/dicts/video/index.js'
import dayjs from 'dayjs'

export default {
  name: 'OperatorChainModel',
  data() {
    return {
      tableType: 'model_operator_orchestration',
      showOperatorList: false,
      operatorTypes,
      businessTypeDict: businessType,
      availableModels: [], // 可选择的模型列表
      selectedOperators: [] // 当前选中的算子
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          id: {
            type: 'text',
            label: 'ID',
            width: 80,
            fixed: 'left',
            hidden: true
          },
          // 模型名称
          value1: {
            type: 'text',
            label: '模型名称',
            width: 200,
            search: {
              type: 'input',
              placeholder: '请输入模型名称'
            },
            form: {
              type: 'input',
              placeholder: '请输入模型名称',
              rules: [
                { required: true, message: '模型名称不能为空', trigger: 'blur' }
              ]
            }
          },
          // 算子链编号
          // value2: {
          //   type: 'text',
          //   label: '算子链编号',
          //   width: 200,
          //   search: {
          //     type: 'input',
          //     placeholder: '请输入算子链编号'
          //   },
          //   form: {
          //     type: 'input',
          //     placeholder: '请输入算子链编号',
          //     rules: [
          //       { required: true, message: '算子链编号不能为空', trigger: 'blur' }
          //     ]
          //   }
          // },
          // 算子组合（关联模型数据）
          value8: {
            type: 'text',
            label: '算子组合',
            width: 200,
            search: {
              type: 'select',
              placeholder: '请选择关联模型',
              clearable: true,
              options: this.availableModels
            },
            form: {
              type: 'select',
              placeholder: '请选择关联模型',
              options: this.availableModels,
              fieldProps: {
                multiple: true,
                class: 'min-w-full'
              },
              parameter: (value) => (value || []).join(','),
              formatter: (data) => {
                return (data.value8 || '').split(',')
              },
              rules: [
                { required: true, message: '请选择关联模型', trigger: 'change' }
              ]
            },
            info: {
              formatter: (data) => data.value8
            }
          },
          // 类型（并联/串联）
          value3: {
            type: 'text',
            label: '类型',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择类型',
              clearable: true,
              options: operatorCombinationType
            },
            form: {
              type: 'select',
              placeholder: '请选择类型',
              options: operatorCombinationType,
              rules: [
                { required: true, message: '请选择类型', trigger: 'change' }
              ],
              value: '串联'
            }
          },
          // 功能描述
          value4: {
            type: 'text',
            label: '功能描述',
            width: 250,
            search: {
              type: 'input',
              placeholder: '请输入功能描述关键词'
            },
            form: {
              type: 'textarea',
              placeholder: '请输入功能描述'
            }
          },
          // 创建人
          value5: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'input',
              placeholder: '请输入创建人'
            },
            add: { value: 'admin' }
          },
          // 创建时间
          value6: {
            type: 'text',
            label: '创建时间',
            width: 160,
            search: { hidden: true },
            form: {
              hidden: true,
              type: 'date-picker',
              props: {
                type: 'datetime',
                placeholder: '请选择创建时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss',
                disabled: true
              }
            },
            add: { value: dayjs().format('MM-DD HH:mm:ss') }
          },
          // 状态
          value7: {
            type: 'text',
            label: '启用/禁用',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择状态',
              clearable: true,
              options: operatorChainStatus
            },
            form: {
              type: 'select',
              placeholder: '请选择状态',
              options: operatorChainStatus,
              rules: [
                { required: true, message: '请选择状态', trigger: 'change' }
              ],
              value: '启用'
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            }
          }
        }
      }
    }
  },
  async mounted() {
    await this.loadAvailableModels()
  },
  methods: {
    // 加载可用的模型列表（仅视频标注类型）
    async loadAvailableModels() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'ai_algorithm_model_management',
            value5: '视频数据标注', // 仅加载视频标注类型的模型
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows && response.rows.length > 0) {
          this.availableModels = response.rows.map(item => ({
            label: item.value2, // 模型名称
            value: item.value2 // 模型ID
          }))
        } else {
          this.availableModels = []
        }
      } catch (error) {
        console.error('加载模型列表失败:', error)
        this.$message.error('加载模型列表失败')
        this.availableModels = []
      }
    },

    // 根据模型ID获取模型名称
    getModelNameById(modelId) {
      const model = this.availableModels.find(item => item.value === modelId)
      return model ? model.label : '未知模型'
    },

    // 获取类型标签类型（并联/串联）
    getCombinationTagType(type) {
      const typeMap = {
        '串联': 'primary',
        '并联': 'success'
      }
      return typeMap[type] || 'info'
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '启用': 'success',
        '禁用': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 复制操作
    async handleCopy(row) {
      try {
        await this.$confirm(`确认要复制当前模型吗？`, '提示', {
          type: 'warning'
        })
      } catch (error) {
        return false
      }

      this.sheetProps.api.add({
        ...row,
        id: void 0,
        action: '复制'
      })

      this.$message.success('复制成功')

      this.$refs.sheetRef.getTableData()
    },

    async onStatusChange(value, row) {
      await this.sheetProps.api.edit({
        ...row,
        value7: value
      })

      this.$message.success('操作成功')
    }

  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}

.model-info {
  max-width: 180px;
  word-break: break-word;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.operator-list-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 600px;
}

.operator-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.operator-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.operator-tag {
  margin: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.operator-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
