<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    width="80%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
    class="video-player-dialog"
    @closed="onClosed"
  >
    <div class="video-container">
      <video
        v-if="videoUrl"
        ref="videoPlayer"
        :src="videoUrl"
        controls
        preload="metadata"
        class="video-player"
        @loadstart="onLoadStart"
        @loadeddata="onLoadedData"
        @error="onError"
      >
        您的浏览器不支持视频播放。
      </video>
      <div v-else class="no-video">
        <i class="el-icon-video-camera"></i>
        <p>暂无视频内容</p>
      </div>
    </div>

    <div v-if="showInfo && videoInfo" class="video-info">
      <div class="info-item">
        <span class="label">文件名：</span>
        <span class="value">{{ videoInfo.fileName }}</span>
      </div>
      <div class="info-item">
        <span class="label">文件大小：</span>
        <span class="value">{{ videoInfo.fileSize }}</span>
      </div>
      <div class="info-item">
        <span class="label">文件格式：</span>
        <span class="value">{{ videoInfo.format }}</span>
      </div>
      <div class="info-item">
        <span class="label">分辨率：</span>
        <span class="value">{{ videoInfo.resolution }}</span>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button v-if="videoUrl" type="primary" @click="handleDownload">下载视频</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'VideoPlayerDialog',
  props: {
    // 是否显示视频信息
    showInfo: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      videoUrl: '',
      title: '视频播放',
      videoInfo: null,
      loading: false
    }
  },
  methods: {
    /**
     * 打开视频播放弹窗
     * @param {Object} options - 配置选项
     * @param {string} options.url - 视频播放地址
     * @param {string} options.title - 弹窗标题
     * @param {Object} options.info - 视频信息对象
     * @param {string} options.info.fileName - 文件名
     * @param {string} options.info.fileSize - 文件大小
     * @param {string} options.info.format - 文件格式
     * @param {string} options.info.resolution - 分辨率
     */
    open(options = {}) {
      this.videoUrl = options.url || ''
      this.title = options.title || '视频播放'
      this.videoInfo = options.info || null
      this.visible = true

      // 如果没有提供视频信息但有URL，尝试从URL获取基本信息
      if (!this.videoInfo && this.videoUrl) {
        this.videoInfo = {
          fileName: this.getFileNameFromUrl(this.videoUrl),
          fileSize: '未知',
          format: this.getFormatFromUrl(this.videoUrl),
          resolution: '未知'
        }
      }
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.visible = false
    },

    /**
     * 弹窗关闭后的回调
     */
    onClosed() {
      // 停止视频播放
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
        this.$refs.videoPlayer.currentTime = 0
      }

      // 清空数据
      this.videoUrl = ''
      this.videoInfo = null
      this.title = '视频播放'

      this.$emit('closed')
    },

    /**
     * 下载视频
     */
    handleDownload() {
      if (!this.videoUrl) {
        this.$message.warning('没有可下载的视频')
        return
      }

      try {
        const link = document.createElement('a')
        link.href = this.videoUrl
        link.download = this.videoInfo?.fileName || 'video'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$message.success('开始下载视频')
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },

    /**
     * 视频开始加载
     */
    onLoadStart() {
      this.loading = true
    },

    /**
     * 视频数据加载完成
     */
    onLoadedData() {
      this.loading = false
    },

    /**
     * 视频加载错误
     */
    onError(event) {
      this.loading = false
      console.error('视频加载失败:', event)
      this.$message.error('视频加载失败，请检查视频地址是否正确')
    },

    /**
     * 从URL中提取文件名
     */
    getFileNameFromUrl(url) {
      try {
        const urlObj = new URL(url)
        const pathname = urlObj.pathname
        return pathname.split('/').pop() || 'video'
      } catch {
        return 'video'
      }
    },

    /**
     * 从URL中提取文件格式
     */
    getFormatFromUrl(url) {
      try {
        const fileName = this.getFileNameFromUrl(url)
        const extension = fileName.split('.').pop()
        return extension ? extension.toUpperCase() : '未知'
      } catch {
        return '未知'
      }
    }
  }
}
</script>

<style scoped>
.video-player-dialog {
  .video-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background-color: #000;
    border-radius: 4px;
    overflow: hidden;
  }

  .video-player {
    width: 100%;
    height: auto;
    max-height: 70vh;
    outline: none;
  }

  .no-video {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 14px;

    i {
      font-size: 48px;
      margin-bottom: 16px;
    }
  }

  .video-info {
    margin-top: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .info-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        color: #606266;
        font-weight: 500;
      }

      .value {
        flex: 1;
        color: #303133;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

/* 深度选择器，修改Element UI的样式 */
.video-player-dialog ::v-deep .el-dialog__body {
  padding: 20px;
}

.video-player-dialog ::v-deep .el-dialog__header {
  padding: 20px 20px 10px;
}
</style>
